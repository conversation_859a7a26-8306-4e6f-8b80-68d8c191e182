<?php
/**
 * ملف الدوال المساعدة
 * Helper Functions File
 */

require_once 'config.php';
require_once 'database.php';

/**
 * دوال إدارة المستخدمين
 */

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

// دالة لطلب تسجيل الدخول
function requireLogin($redirectTo = null) {
    if (!isLoggedIn()) {
        $redirect = $redirectTo ?: $_SERVER['REQUEST_URI'];
        redirect('login.php?redirect=' . urlencode($redirect));
    }
}

// دالة للحصول على المستخدم الحالي
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    $db = Database::getInstance();
    return $db->query("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']])->fetch();
}

// دالة لتسجيل الخروج
function logoutUser() {
    session_destroy();
    setcookie('remember_token', '', time() - 3600, '/');
    return true;
}

// دالة لتنسيق التاريخ النسبي
function formatRelativeDate($date) {
    $timestamp = strtotime($date);
    $diff = time() - $timestamp;

    if ($diff < 60) {
        return 'منذ لحظات';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return "منذ {$minutes} دقيقة";
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return "منذ {$hours} ساعة";
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return "منذ {$days} يوم";
    } else {
        return formatDate($date);
    }
}

// دالة لتعيين رسالة الجلسة
function setSessionMessage($message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
}

// دالة للحصول على رسالة الجلسة
function getSessionMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * دوال إدارة الإعلانات
 */

// دالة للحصول على الإعلانات
function getAds($filters = [], $page = 1, $limit = null) {
    $db = Database::getInstance();
    $limit = $limit ?: getSetting('items_per_page', 12);
    $offset = ($page - 1) * $limit;

    $where = ['status = ?'];
    $params = ['active'];

    // تطبيق الفلاتر
    if (!empty($filters['search'])) {
        $where[] = '(title LIKE ? OR description LIKE ?)';
        $searchTerm = '%' . $filters['search'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    if (!empty($filters['category_id'])) {
        $where[] = 'category_id = ?';
        $params[] = $filters['category_id'];
    }

    if (!empty($filters['location'])) {
        $where[] = 'location LIKE ?';
        $params[] = '%' . $filters['location'] . '%';
    }

    if (!empty($filters['min_price'])) {
        $where[] = 'price >= ?';
        $params[] = $filters['min_price'];
    }

    if (!empty($filters['max_price'])) {
        $where[] = 'price <= ?';
        $params[] = $filters['max_price'];
    }

    if (!empty($filters['price_type'])) {
        $where[] = 'price_type = ?';
        $params[] = $filters['price_type'];
    }

    if (!empty($filters['featured'])) {
        $where[] = 'featured = 1';
    }

    if (!empty($filters['user_id'])) {
        $where[] = 'user_id = ?';
        $params[] = $filters['user_id'];
    }

    // ترتيب النتائج
    $orderBy = 'created_at DESC';
    if (!empty($filters['sort'])) {
        switch ($filters['sort']) {
            case 'oldest':
                $orderBy = 'created_at ASC';
                break;
            case 'price_low':
                $orderBy = 'price ASC';
                break;
            case 'price_high':
                $orderBy = 'price DESC';
                break;
            case 'most_viewed':
                $orderBy = 'views DESC';
                break;
            case 'relevance':
                if (!empty($filters['search'])) {
                    $orderBy = 'featured DESC, urgent DESC, created_at DESC';
                }
                break;
        }
    }

    $whereClause = implode(' AND ', $where);

    // الحصول على العدد الإجمالي
    $totalQuery = "SELECT COUNT(*) as total FROM ads WHERE {$whereClause}";
    $total = $db->query($totalQuery, $params)->fetch()['total'];

    // الحصول على البيانات
    $query = "
        SELECT a.*, c.name_ar as category_name, u.full_name as user_name
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE {$whereClause}
        ORDER BY {$orderBy}
        LIMIT {$limit} OFFSET {$offset}
    ";

    $ads = $db->query($query, $params)->fetchAll();

    // معالجة الصور
    foreach ($ads as &$ad) {
        if (!empty($ad['images'])) {
            $ad['images'] = json_decode($ad['images'], true);
        }
    }

    return [
        'data' => $ads,
        'total_items' => $total,
        'page' => $page,
        'limit' => $limit,
        'total_pages' => ceil($total / $limit),
        'has_prev' => $page > 1,
        'has_next' => $page < ceil($total / $limit)
    ];
}

// دالة للحصول على إعلان واحد
function getAdById($id) {
    $db = Database::getInstance();
    $ad = $db->query("
        SELECT a.*, c.name_ar as category_name, u.full_name as user_name
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.id = ?
    ", [$id])->fetch();

    if ($ad && !empty($ad['images'])) {
        $ad['images'] = json_decode($ad['images'], true);
    }

    return $ad;
}

// دالة لزيادة عدد المشاهدات
function incrementAdViews($id) {
    $db = Database::getInstance();
    return $db->query("UPDATE ads SET views = views + 1 WHERE id = ?", [$id]);
}

// دالة للحصول على الفئات
function getCategoryTree() {
    $db = Database::getInstance();
    $categories = $db->query("SELECT * FROM categories WHERE parent_id IS NULL ORDER BY sort_order, name_ar")->fetchAll();

    foreach ($categories as &$category) {
        $category['children'] = $db->query("SELECT * FROM categories WHERE parent_id = ? ORDER BY sort_order, name_ar", [$category['id']])->fetchAll();
    }

    return $categories;
}

// دالة للحصول على فئة واحدة
function getCategoryById($id) {
    $db = Database::getInstance();
    return $db->query("SELECT * FROM categories WHERE id = ?", [$id])->fetch();
}

// دالة للحصول على الفئات الفرعية
function getSubcategories($parentId) {
    $db = Database::getInstance();
    return $db->query("SELECT * FROM categories WHERE parent_id = ? ORDER BY sort_order, name_ar", [$parentId])->fetchAll();
}

// دالة للتحقق من صحة بيانات الإعلان
function validateAdData($data) {
    $errors = [];

    if (empty($data['title'])) {
        $errors[] = 'عنوان الإعلان مطلوب';
    } elseif (strlen($data['title']) < 5) {
        $errors[] = 'عنوان الإعلان يجب أن يكون 5 أحرف على الأقل';
    }

    if (empty($data['description'])) {
        $errors[] = 'وصف الإعلان مطلوب';
    } elseif (strlen($data['description']) < 20) {
        $errors[] = 'وصف الإعلان يجب أن يكون 20 حرف على الأقل';
    }

    if (empty($data['category_id'])) {
        $errors[] = 'يجب اختيار فئة للإعلان';
    }

    if (empty($data['location'])) {
        $errors[] = 'الموقع مطلوب';
    }

    if (!empty($data['price']) && $data['price'] < 0) {
        $errors[] = 'السعر لا يمكن أن يكون سالباً';
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}
