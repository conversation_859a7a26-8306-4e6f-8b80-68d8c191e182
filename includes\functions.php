<?php
/**
 * ملف الوظائف المساعدة
 * Helper Functions for Classified Ads Website
 */

require_once 'config.php';
require_once 'database.php';

/**
 * وظائف إدارة المستخدمين
 */

// دالة لتسجيل الدخول
function loginUser($email, $password, $remember = false) {
    $db = Database::getInstance();
    
    // البحث عن المستخدم
    $user = $db->query("SELECT * FROM users WHERE email = ? AND status = 'active'", [$email])->fetch();
    
    if ($user && verifyPassword($password, $user['password'])) {
        // تحديث آخر تسجيل دخول
        $db->query("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
        
        // حفظ بيانات المستخدم في الجلسة
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['full_name'];
        $_SESSION['user_type'] = $user['user_type'];
        $_SESSION['logged_in'] = true;
        
        // إذا كان المستخدم يريد تذكر تسجيل الدخول
        if ($remember) {
            $token = bin2hex(random_bytes(32));
            setcookie('remember_token', $token, time() + (86400 * 30), '/'); // 30 يوم
            // يمكن حفظ الرمز في قاعدة البيانات للتحقق لاحقاً
        }
        
        return true;
    }
    
    return false;
}

// دالة لتسجيل الخروج
function logoutUser() {
    session_destroy();
    setcookie('remember_token', '', time() - 3600, '/');
    return true;
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

// دالة لطلب تسجيل الدخول
function requireLogin($redirectTo = null) {
    if (!isLoggedIn()) {
        $redirect = $redirectTo ?: $_SERVER['REQUEST_URI'];
        redirect('login.php?redirect=' . urlencode($redirect));
    }
}

// دالة للحصول على المستخدم الحالي
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    $db = Database::getInstance();
    return $db->query("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']])->fetch();
}

// ملاحظة: دوال التشفير والتحقق موجودة في config.php

// دالة لإعادة التوجيه
function redirect($url) {
    if (!headers_sent()) {
        header('Location: ' . $url);
        exit;
    } else {
        echo '<script>window.location.href = "' . $url . '";</script>';
        exit;
    }
}

// دالة لإنشاء رابط
function url($path = '') {
    $baseUrl = rtrim(SITE_URL, '/');
    $path = ltrim($path, '/');
    return $baseUrl . '/' . $path;
}

// دالة لإنشاء رابط الأصول
function asset($path) {
    return url('assets/' . ltrim($path, '/'));
}

// دالة لتنسيق التاريخ
function formatDate($date, $format = 'd/m/Y') {
    if ($format === 'relative') {
        $timestamp = strtotime($date);
        $diff = time() - $timestamp;

        if ($diff < 60) {
            return 'منذ لحظات';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return "منذ {$minutes} دقيقة";
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return "منذ {$hours} ساعة";
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return "منذ {$days} يوم";
        } else {
            return date('d/m/Y', $timestamp);
        }
    }

    return date($format, strtotime($date));
}

// دالة للحصول على إعداد
function getSetting($key, $default = null) {
    static $settings = null;

    if ($settings === null) {
        $settings = [
            'site_name' => 'موقع الإعلانات المبوبة',
            'site_description' => 'أفضل موقع للإعلانات المبوبة في المملكة العربية السعودية',
            'admin_email' => '<EMAIL>',
            'items_per_page' => 12,
            'max_images_per_ad' => 5,
            'max_file_size' => 5 * 1024 * 1024, // 5MB
        ];
    }

    return isset($settings[$key]) ? $settings[$key] : $default;
}

// دالة لتعيين رسالة الجلسة
function setSessionMessage($message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
}

// دالة للحصول على رسالة الجلسة
function getSessionMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * دوال إدارة الإعلانات
 */

// دالة للحصول على الإعلانات
function getAds($filters = [], $page = 1, $limit = null) {
    $db = Database::getInstance();
    $limit = $limit ?: getSetting('items_per_page', 12);
    $offset = ($page - 1) * $limit;

    $where = ['status = ?'];
    $params = ['active'];

    // تطبيق الفلاتر
    if (!empty($filters['search'])) {
        $where[] = '(title LIKE ? OR description LIKE ?)';
        $searchTerm = '%' . $filters['search'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    if (!empty($filters['category_id'])) {
        $where[] = 'category_id = ?';
        $params[] = $filters['category_id'];
    }

    if (!empty($filters['location'])) {
        $where[] = 'location LIKE ?';
        $params[] = '%' . $filters['location'] . '%';
    }

    if (!empty($filters['min_price'])) {
        $where[] = 'price >= ?';
        $params[] = $filters['min_price'];
    }

    if (!empty($filters['max_price'])) {
        $where[] = 'price <= ?';
        $params[] = $filters['max_price'];
    }

    if (!empty($filters['price_type'])) {
        $where[] = 'price_type = ?';
        $params[] = $filters['price_type'];
    }

    if (!empty($filters['featured'])) {
        $where[] = 'featured = 1';
    }

    if (!empty($filters['user_id'])) {
        $where[] = 'user_id = ?';
        $params[] = $filters['user_id'];
    }

    // ترتيب النتائج
    $orderBy = 'created_at DESC';
    if (!empty($filters['sort'])) {
        switch ($filters['sort']) {
            case 'oldest':
                $orderBy = 'created_at ASC';
                break;
            case 'price_low':
                $orderBy = 'price ASC';
                break;
            case 'price_high':
                $orderBy = 'price DESC';
                break;
            case 'most_viewed':
                $orderBy = 'views DESC';
                break;
            case 'relevance':
                if (!empty($filters['search'])) {
                    $orderBy = 'featured DESC, urgent DESC, created_at DESC';
                }
                break;
        }
    }

    $whereClause = implode(' AND ', $where);

    // الحصول على العدد الإجمالي
    $totalQuery = "SELECT COUNT(*) as total FROM ads WHERE {$whereClause}";
    $total = $db->query($totalQuery, $params)->fetch()['total'];

    // الحصول على البيانات
    $query = "
        SELECT a.*, c.name_ar as category_name, u.full_name as user_name
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE {$whereClause}
        ORDER BY {$orderBy}
        LIMIT {$limit} OFFSET {$offset}
    ";

    $ads = $db->query($query, $params)->fetchAll();

    // معالجة الصور
    foreach ($ads as &$ad) {
        if (!empty($ad['images'])) {
            $ad['images'] = json_decode($ad['images'], true);
        }
    }

    return [
        'data' => $ads,
        'total_items' => $total,
        'page' => $page,
        'limit' => $limit,
        'total_pages' => ceil($total / $limit),
        'has_prev' => $page > 1,
        'has_next' => $page < ceil($total / $limit)
    ];
}

// دالة للحصول على إعلان واحد
function getAdById($id) {
    $db = Database::getInstance();
    $ad = $db->query("
        SELECT a.*, c.name_ar as category_name, u.full_name as user_name
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.id = ?
    ", [$id])->fetch();

    if ($ad && !empty($ad['images'])) {
        $ad['images'] = json_decode($ad['images'], true);
    }

    return $ad;
}

// دالة لزيادة عدد المشاهدات
function incrementAdViews($id) {
    $db = Database::getInstance();
    return $db->query("UPDATE ads SET views = views + 1 WHERE id = ?", [$id]);
}

// دالة للحصول على الفئات
function getCategoryTree() {
    $db = Database::getInstance();
    $categories = $db->query("SELECT * FROM categories WHERE parent_id IS NULL ORDER BY sort_order, name_ar")->fetchAll();

    foreach ($categories as &$category) {
        $category['children'] = $db->query("SELECT * FROM categories WHERE parent_id = ? ORDER BY sort_order, name_ar", [$category['id']])->fetchAll();
    }

    return $categories;
}

// دالة للحصول على فئة واحدة
function getCategoryById($id) {
    $db = Database::getInstance();
    return $db->query("SELECT * FROM categories WHERE id = ?", [$id])->fetch();
}

// دالة للحصول على الفئات الفرعية
function getSubcategories($parentId) {
    $db = Database::getInstance();
    return $db->query("SELECT * FROM categories WHERE parent_id = ? ORDER BY sort_order, name_ar", [$parentId])->fetchAll();
}

// دالة للتحقق من صحة بيانات الإعلان
function validateAdData($data) {
    $errors = [];

    if (empty($data['title'])) {
        $errors[] = 'عنوان الإعلان مطلوب';
    } elseif (strlen($data['title']) < 5) {
        $errors[] = 'عنوان الإعلان يجب أن يكون 5 أحرف على الأقل';
    }

    if (empty($data['description'])) {
        $errors[] = 'وصف الإعلان مطلوب';
    } elseif (strlen($data['description']) < 20) {
        $errors[] = 'وصف الإعلان يجب أن يكون 20 حرف على الأقل';
    }

    if (empty($data['category_id'])) {
        $errors[] = 'يجب اختيار فئة للإعلان';
    }

    if (empty($data['location'])) {
        $errors[] = 'الموقع مطلوب';
    }

    if (!empty($data['price']) && $data['price'] < 0) {
        $errors[] = 'السعر لا يمكن أن يكون سالباً';
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

// دالة لإنشاء إعلان جديد
function createAd($data) {
    try {
        $db = Database::getInstance();

        $query = "
            INSERT INTO ads (user_id, title, description, category_id, price, price_type,
                           location, phone, whatsapp, images, featured, urgent, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
        ";

        $images = !empty($data['images']) ? json_encode($data['images']) : null;

        $result = $db->query($query, [
            $data['user_id'],
            $data['title'],
            $data['description'],
            $data['category_id'],
            $data['price'] ?? 0,
            $data['price_type'] ?? 'fixed',
            $data['location'],
            $data['phone'] ?? null,
            $data['whatsapp'] ?? null,
            $images,
            $data['featured'] ?? 0,
            $data['urgent'] ?? 0
        ]);

        return ['success' => true, 'id' => $db->lastInsertId()];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء إنشاء الإعلان'];
    }
}

// دالة لرفع الصور
function uploadAdImages($files) {
    $uploadedFiles = [];
    $errors = [];

    $uploadDir = SITE_ROOT . '/assets/uploads/ads/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    $maxFiles = MAX_IMAGES_PER_AD;
    $maxSize = MAX_FILE_SIZE;
    $allowedTypes = ALLOWED_IMAGE_TYPES;

    if (count($files['name']) > $maxFiles) {
        return ['success' => false, 'message' => "يمكنك رفع حتى {$maxFiles} صور فقط"];
    }

    for ($i = 0; $i < count($files['name']); $i++) {
        if ($files['error'][$i] !== UPLOAD_ERR_OK) {
            continue;
        }

        $fileName = $files['name'][$i];
        $fileSize = $files['size'][$i];
        $fileTmp = $files['tmp_name'][$i];
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        // فحص نوع الملف
        if (!in_array($fileExt, $allowedTypes)) {
            $errors[] = "نوع الملف {$fileName} غير مدعوم";
            continue;
        }

        // فحص حجم الملف
        if ($fileSize > $maxSize) {
            $errors[] = "حجم الملف {$fileName} كبير جداً";
            continue;
        }

        // إنشاء اسم فريد للملف
        $newFileName = uniqid() . '_' . time() . '.' . $fileExt;
        $targetPath = $uploadDir . $newFileName;

        if (move_uploaded_file($fileTmp, $targetPath)) {
            $uploadedFiles[] = $newFileName;
        } else {
            $errors[] = "فشل في رفع الملف {$fileName}";
        }
    }

    if (!empty($errors)) {
        return ['success' => false, 'message' => implode(', ', $errors)];
    }

    return ['success' => true, 'files' => $uploadedFiles];
}

// دالة للحصول على إعلانات المستخدم
function getUserAds($userId, $filters = [], $page = 1) {
    $filters['user_id'] = $userId;
    return getAds($filters, $page);
}

// دالة للحصول على المفضلة
function getUserFavorites($userId, $page = 1) {
    $db = Database::getInstance();
    $limit = getSetting('items_per_page', 12);
    $offset = ($page - 1) * $limit;

    // الحصول على العدد الإجمالي
    $totalQuery = "
        SELECT COUNT(*) as total
        FROM favorites f
        JOIN ads a ON f.ad_id = a.id
        WHERE f.user_id = ? AND a.status = 'active'
    ";
    $total = $db->query($totalQuery, [$userId])->fetch()['total'];

    // الحصول على البيانات
    $query = "
        SELECT a.*, c.name_ar as category_name, u.full_name as user_name
        FROM favorites f
        JOIN ads a ON f.ad_id = a.id
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE f.user_id = ? AND a.status = 'active'
        ORDER BY f.created_at DESC
        LIMIT {$limit} OFFSET {$offset}
    ";

    $favorites = $db->query($query, [$userId])->fetchAll();

    // معالجة الصور
    foreach ($favorites as &$favorite) {
        if (!empty($favorite['images'])) {
            $favorite['images'] = json_decode($favorite['images'], true);
        }
    }

    return [
        'data' => $favorites,
        'total_items' => $total,
        'page' => $page,
        'limit' => $limit,
        'total_pages' => ceil($total / $limit),
        'has_prev' => $page > 1,
        'has_next' => $page < ceil($total / $limit)
    ];
}

// دالة لحذف إعلان المستخدم
function deleteUserAd($userId, $adId) {
    try {
        $db = Database::getInstance();

        // التحقق من ملكية الإعلان
        $ad = $db->query("SELECT * FROM ads WHERE id = ? AND user_id = ?", [$adId, $userId])->fetch();
        if (!$ad) {
            return ['success' => false, 'message' => 'الإعلان غير موجود أو ليس لديك صلاحية لحذفه'];
        }

        // حذف الصور
        if (!empty($ad['images'])) {
            $images = json_decode($ad['images'], true);
            foreach ($images as $image) {
                $imagePath = SITE_ROOT . '/assets/uploads/ads/' . $image;
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }
        }

        // حذف الإعلان
        $db->query("DELETE FROM ads WHERE id = ?", [$adId]);

        return ['success' => true, 'message' => 'تم حذف الإعلان بنجاح'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء حذف الإعلان'];
    }
}

// دالة لتبديل حالة الإعلان
function toggleAdStatus($userId, $adId) {
    try {
        $db = Database::getInstance();

        // التحقق من ملكية الإعلان
        $ad = $db->query("SELECT * FROM ads WHERE id = ? AND user_id = ?", [$adId, $userId])->fetch();
        if (!$ad) {
            return ['success' => false, 'message' => 'الإعلان غير موجود أو ليس لديك صلاحية لتعديله'];
        }

        $newStatus = $ad['status'] === 'active' ? 'inactive' : 'active';
        $db->query("UPDATE ads SET status = ? WHERE id = ?", [$newStatus, $adId]);

        return ['success' => true, 'message' => 'تم تحديث حالة الإعلان بنجاح'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء تحديث الإعلان'];
    }
}

// دالة لإزالة من المفضلة
function removeFavorite($userId, $adId) {
    try {
        $db = Database::getInstance();
        $db->query("DELETE FROM favorites WHERE user_id = ? AND ad_id = ?", [$userId, $adId]);
        return ['success' => true, 'message' => 'تم حذف الإعلان من المفضلة'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء الحذف'];
    }
}

// دالة للحصول على الإعلانات المشابهة
function getSimilarAds($adId, $categoryId, $limit = 4) {
    $db = Database::getInstance();

    $query = "
        SELECT a.*, c.name_ar as category_name
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        WHERE a.id != ? AND a.category_id = ? AND a.status = 'active'
        ORDER BY a.created_at DESC
        LIMIT ?
    ";

    $ads = $db->query($query, [$adId, $categoryId, $limit])->fetchAll();

    // معالجة الصور
    foreach ($ads as &$ad) {
        if (!empty($ad['images'])) {
            $ad['images'] = json_decode($ad['images'], true);
        }
    }

    return $ads;
}

// دالة للحصول على بيانات المستخدم
function getUserById($id) {
    $db = Database::getInstance();
    return $db->query("SELECT * FROM users WHERE id = ?", [$id])->fetch();
}

/**
 * دوال المصادقة والتسجيل
 */

// دالة لتسجيل الدخول
function loginUser($email, $password, $remember = false) {
    try {
        $db = Database::getInstance();

        // البحث عن المستخدم
        $user = $db->query("SELECT * FROM users WHERE email = ? AND status = 'active'", [$email])->fetch();

        if (!$user || !password_verify($password, $user['password'])) {
            return ['success' => false, 'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة'];
        }

        // تسجيل الدخول
        $_SESSION['logged_in'] = true;
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['full_name'];

        // تذكرني
        if ($remember) {
            $token = bin2hex(random_bytes(32));
            setcookie('remember_token', $token, time() + SESSION_LIFETIME, '/');
            $db->query("UPDATE users SET remember_token = ? WHERE id = ?", [$token, $user['id']]);
        }

        // تحديث آخر دخول
        $db->query("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);

        return ['success' => true, 'message' => 'تم تسجيل الدخول بنجاح'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء تسجيل الدخول'];
    }
}

// دالة للتحقق من صحة بيانات التسجيل
function validateRegistrationData($data) {
    $errors = [];

    if (empty($data['full_name'])) {
        $errors[] = 'الاسم الكامل مطلوب';
    } elseif (strlen($data['full_name']) < 3) {
        $errors[] = 'الاسم الكامل يجب أن يكون 3 أحرف على الأقل';
    }

    if (empty($data['email'])) {
        $errors[] = 'البريد الإلكتروني مطلوب';
    } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    } else {
        // فحص وجود البريد الإلكتروني
        $db = Database::getInstance();
        $existing = $db->query("SELECT id FROM users WHERE email = ?", [$data['email']])->fetch();
        if ($existing) {
            $errors[] = 'البريد الإلكتروني مستخدم بالفعل';
        }
    }

    if (empty($data['password'])) {
        $errors[] = 'كلمة المرور مطلوبة';
    } elseif (strlen($data['password']) < 8) {
        $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    } elseif ($data['password'] !== $data['confirm_password']) {
        $errors[] = 'كلمات المرور غير متطابقة';
    }

    if (!$data['agree_terms']) {
        $errors[] = 'يجب الموافقة على الشروط والأحكام';
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

// دالة لتسجيل مستخدم جديد
function registerUser($data) {
    try {
        $db = Database::getInstance();

        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        $verificationToken = bin2hex(random_bytes(32));

        $query = "
            INSERT INTO users (full_name, email, password, phone, user_type, verification_token, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())
        ";

        $db->query($query, [
            $data['full_name'],
            $data['email'],
            $hashedPassword,
            $data['phone'] ?? null,
            $data['user_type'] ?? 'individual',
            $verificationToken
        ]);

        // إرسال بريد التفعيل (يمكن تنفيذه لاحقاً)
        // sendVerificationEmail($data['email'], $verificationToken);

        return ['success' => true, 'message' => 'تم إنشاء حسابك بنجاح'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء إنشاء الحساب'];
    }
}

// دالة لتحديث الملف الشخصي
function updateUserProfile($userId, $data) {
    try {
        $db = Database::getInstance();

        $query = "UPDATE users SET full_name = ?, phone = ?, user_type = ? WHERE id = ?";
        $db->query($query, [$data['full_name'], $data['phone'], $data['user_type'], $userId]);

        return ['success' => true, 'message' => 'تم تحديث بياناتك بنجاح'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء التحديث'];
    }
}

// دالة لتغيير كلمة المرور
function changeUserPassword($userId, $currentPassword, $newPassword) {
    try {
        $db = Database::getInstance();

        // التحقق من كلمة المرور الحالية
        $user = $db->query("SELECT password FROM users WHERE id = ?", [$userId])->fetch();
        if (!$user || !password_verify($currentPassword, $user['password'])) {
            return ['success' => false, 'message' => 'كلمة المرور الحالية غير صحيحة'];
        }

        // تحديث كلمة المرور
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $db->query("UPDATE users SET password = ? WHERE id = ?", [$hashedPassword, $userId]);

        return ['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء تغيير كلمة المرور'];
    }
}

// دالة لطلب إعادة تعيين كلمة المرور
function requestPasswordReset($email) {
    try {
        $db = Database::getInstance();

        // التحقق من وجود المستخدم
        $user = $db->query("SELECT id FROM users WHERE email = ?", [$email])->fetch();
        if (!$user) {
            return ['success' => false, 'message' => 'البريد الإلكتروني غير موجود'];
        }

        // إنشاء رمز إعادة التعيين
        $resetToken = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + 3600); // ساعة واحدة

        $db->query("UPDATE users SET reset_token = ?, reset_expires = ? WHERE id = ?",
                  [$resetToken, $expiresAt, $user['id']]);

        // إرسال بريد إعادة التعيين (يمكن تنفيذه لاحقاً)
        // sendPasswordResetEmail($email, $resetToken);

        return ['success' => true, 'message' => 'تم إرسال رابط إعادة التعيين'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء الطلب'];
    }
}

// دالة لإعادة تعيين كلمة المرور
function resetPassword($token, $newPassword) {
    try {
        $db = Database::getInstance();

        // التحقق من صحة الرمز
        $user = $db->query("SELECT id FROM users WHERE reset_token = ? AND reset_expires > NOW()", [$token])->fetch();
        if (!$user) {
            return ['success' => false, 'message' => 'رابط إعادة التعيين غير صحيح أو منتهي الصلاحية'];
        }

        // تحديث كلمة المرور
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $db->query("UPDATE users SET password = ?, reset_token = NULL, reset_expires = NULL WHERE id = ?",
                  [$hashedPassword, $user['id']]);

        return ['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء إعادة التعيين'];
    }
}

// دالة لإرسال رسالة اتصال
function sendContactMessage($name, $email, $subject, $message) {
    try {
        $db = Database::getInstance();

        $query = "INSERT INTO contact_messages (name, email, subject, message, created_at) VALUES (?, ?, ?, ?, NOW())";
        $db->query($query, [$name, $email, $subject, $message]);

        return ['success' => true, 'message' => 'تم إرسال رسالتك بنجاح'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء إرسال الرسالة'];
    }
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

// دالة للحصول على المستخدم الحالي
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    $db = Database::getInstance();
    return $db->find('users', $_SESSION['user_id']);
}

// دالة لتسجيل مستخدم جديد
function registerUser($data) {
    $db = Database::getInstance();
    
    // التحقق من عدم وجود البريد الإلكتروني
    if ($db->exists('users', ['email = ?'], [$data['email']])) {
        return ['success' => false, 'message' => 'البريد الإلكتروني مستخدم بالفعل'];
    }
    
    // التحقق من عدم وجود اسم المستخدم
    if ($db->exists('users', ['username = ?'], [$data['username']])) {
        return ['success' => false, 'message' => 'اسم المستخدم مستخدم بالفعل'];
    }
    
    // تشفير كلمة المرور
    $data['password'] = hashPassword($data['password']);
    $data['verification_token'] = bin2hex(random_bytes(32));
    
    try {
        $userId = $db->insert('users', $data)->lastInsertId();
        
        // إرسال بريد التفعيل (يمكن تنفيذه لاحقاً)
        // sendVerificationEmail($data['email'], $data['verification_token']);
        
        return ['success' => true, 'user_id' => $userId];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء التسجيل'];
    }
}

/**
 * وظائف إدارة الإعلانات
 */

// دالة للحصول على الإعلانات مع الترقيم
function getAds($filters = [], $page = 1, $perPage = null) {
    if ($perPage === null) {
        $perPage = ADS_PER_PAGE;
    }
    
    $db = Database::getInstance();
    $conditions = ["a.status = 'active'"];
    $params = [];
    
    // فلترة حسب القسم
    if (!empty($filters['category_id'])) {
        $conditions[] = "a.category_id = ?";
        $params[] = $filters['category_id'];
    }
    
    // فلترة حسب الموقع
    if (!empty($filters['location'])) {
        $conditions[] = "a.location LIKE ?";
        $params[] = '%' . $filters['location'] . '%';
    }
    
    // فلترة حسب السعر
    if (!empty($filters['min_price'])) {
        $conditions[] = "a.price >= ?";
        $params[] = $filters['min_price'];
    }
    
    if (!empty($filters['max_price'])) {
        $conditions[] = "a.price <= ?";
        $params[] = $filters['max_price'];
    }
    
    // البحث النصي
    if (!empty($filters['search'])) {
        $conditions[] = "(a.title LIKE ? OR a.description LIKE ?)";
        $params[] = '%' . $filters['search'] . '%';
        $params[] = '%' . $filters['search'] . '%';
    }
    
    // الترتيب
    $orderBy = "a.featured DESC, a.created_at DESC";
    if (!empty($filters['sort'])) {
        switch ($filters['sort']) {
            case 'price_low':
                $orderBy = "a.price ASC";
                break;
            case 'price_high':
                $orderBy = "a.price DESC";
                break;
            case 'newest':
                $orderBy = "a.created_at DESC";
                break;
            case 'oldest':
                $orderBy = "a.created_at ASC";
                break;
        }
    }
    
    $sql = "
        SELECT a.*, c.name_ar as category_name, u.full_name as user_name
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE " . implode(' AND ', $conditions) . "
        ORDER BY $orderBy
    ";
    
    return $db->paginate($sql, $params, $page, $perPage);
}

// دالة للحصول على إعلان واحد
function getAd($id) {
    $db = Database::getInstance();
    
    $sql = "
        SELECT a.*, c.name_ar as category_name, u.full_name as user_name, u.phone as user_phone
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.id = ? AND a.status = 'active'
    ";
    
    $ad = $db->query($sql, [$id])->fetch();
    
    if ($ad) {
        // زيادة عدد المشاهدات
        $db->query("UPDATE ads SET views = views + 1 WHERE id = ?", [$id]);
        
        // تسجيل الإحصائية
        recordStatistic($id, 'view');
        
        // الحصول على الصور
        $images = $db->query("SELECT * FROM ad_images WHERE ad_id = ? ORDER BY sort_order", [$id])->fetchAll();
        $ad['images'] = $images;
    }
    
    return $ad;
}

// دالة لإضافة إعلان جديد
function createAd($data, $images = []) {
    $db = Database::getInstance();
    
    try {
        $db->beginTransaction();
        
        // إدراج الإعلان
        $adId = $db->insert('ads', $data)->lastInsertId();
        
        // رفع الصور
        if (!empty($images)) {
            foreach ($images as $index => $image) {
                $imagePath = uploadImage($image, 'ads');
                if ($imagePath) {
                    $imageData = [
                        'ad_id' => $adId,
                        'image_path' => $imagePath,
                        'image_name' => $image['name'],
                        'is_primary' => $index === 0 ? 1 : 0,
                        'sort_order' => $index
                    ];
                    $db->insert('ad_images', $imageData);
                }
            }
        }
        
        $db->commit();
        return ['success' => true, 'ad_id' => $adId];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'حدث خطأ أثناء إضافة الإعلان'];
    }
}

/**
 * وظائف إدارة الأقسام
 */

// دالة للحصول على جميع الأقسام
function getCategories($parentId = null, $status = 'active') {
    $db = Database::getInstance();
    
    $conditions = ["status = ?"];
    $params = [$status];
    
    if ($parentId === null) {
        $conditions[] = "parent_id IS NULL";
    } else {
        $conditions[] = "parent_id = ?";
        $params[] = $parentId;
    }
    
    return $db->findAll('categories', $conditions, $params, 'sort_order ASC, name_ar ASC');
}

// دالة للحصول على شجرة الأقسام
function getCategoryTree() {
    $categories = getCategories();
    $tree = [];
    
    foreach ($categories as $category) {
        $category['children'] = getCategories($category['id']);
        $tree[] = $category;
    }
    
    return $tree;
}

/**
 * وظائف رفع الملفات
 */

// دالة لرفع صورة
function uploadImage($file, $folder = 'general') {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return false;
    }
    
    // التحقق من نوع الملف
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, ALLOWED_IMAGE_TYPES)) {
        return false;
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    // إنشاء اسم فريد للملف
    $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
    $uploadPath = UPLOAD_PATH . $folder . '/';
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($uploadPath)) {
        mkdir($uploadPath, 0755, true);
    }
    
    $fullPath = $uploadPath . $fileName;
    
    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $fullPath)) {
        // تحسين الصورة (اختياري)
        optimizeImage($fullPath);
        
        return $folder . '/' . $fileName;
    }
    
    return false;
}

// دالة لتحسين الصورة
function optimizeImage($imagePath, $quality = 80) {
    $imageInfo = getimagesize($imagePath);
    if (!$imageInfo) return false;
    
    $imageType = $imageInfo[2];
    
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $image = imagecreatefromjpeg($imagePath);
            imagejpeg($image, $imagePath, $quality);
            break;
        case IMAGETYPE_PNG:
            $image = imagecreatefrompng($imagePath);
            imagepng($image, $imagePath, 9);
            break;
        case IMAGETYPE_GIF:
            $image = imagecreatefromgif($imagePath);
            imagegif($image, $imagePath);
            break;
    }
    
    if (isset($image)) {
        imagedestroy($image);
        return true;
    }
    
    return false;
}

/**
 * وظائف الإحصائيات
 */

// دالة لتسجيل إحصائية
function recordStatistic($adId, $actionType, $userId = null, $searchQuery = null) {
    $db = Database::getInstance();
    
    $data = [
        'ad_id' => $adId,
        'user_id' => $userId,
        'action_type' => $actionType,
        'ip_address' => getRealIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'referrer' => $_SERVER['HTTP_REFERER'] ?? '',
        'search_query' => $searchQuery
    ];
    
    try {
        $db->insert('statistics', $data);
    } catch (Exception $e) {
        // تجاهل أخطاء الإحصائيات
    }
}

/**
 * وظائف التحقق من الصلاحيات
 */

// دالة للتحقق من ملكية الإعلان
function canEditAd($adId, $userId = null) {
    if ($userId === null && !isLoggedIn()) {
        return false;
    }
    
    $userId = $userId ?? $_SESSION['user_id'];
    $db = Database::getInstance();
    
    return $db->exists('ads', ['id = ? AND user_id = ?'], [$adId, $userId]);
}

/**
 * وظائف البريد الإلكتروني
 */

// دالة لإرسال بريد إلكتروني
function sendEmail($to, $subject, $body, $isHTML = true) {
    // يمكن استخدام PHPMailer أو أي مكتبة أخرى
    // هذا مثال بسيط باستخدام mail() function
    
    $headers = "From: " . ADMIN_EMAIL . "\r\n";
    $headers .= "Reply-To: " . ADMIN_EMAIL . "\r\n";
    
    if ($isHTML) {
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    } else {
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    }
    
    return mail($to, $subject, $body, $headers);
}

/**
 * وظائف التنظيف والتحقق
 */

// دالة للتحقق من صحة رقم الهاتف
function isValidPhone($phone) {
    return preg_match('/^[0-9+\-\s()]{10,15}$/', $phone);
}

// دالة للتحقق من صحة السعر
function isValidPrice($price) {
    return is_numeric($price) && $price >= 0;
}

// دالة لتنظيف النص من HTML
function stripHtml($text) {
    return strip_tags($text);
}

// دالة لاقتطاع النص
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * وظائف مساعدة للعرض
 */

// دالة لعرض رسائل التنبيه
function showAlert($message, $type = 'info') {
    $alertClass = [
        'success' => 'alert-success',
        'error' => 'alert-danger',
        'warning' => 'alert-warning',
        'info' => 'alert-info'
    ];
    
    $class = $alertClass[$type] ?? 'alert-info';
    
    return "<div class='alert $class alert-dismissible fade show' role='alert'>
                $message
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}

// دالة لعرض رسائل الجلسة
function showSessionMessages() {
    $output = '';
    
    if (isset($_SESSION['success'])) {
        $output .= showAlert($_SESSION['success'], 'success');
        unset($_SESSION['success']);
    }
    
    if (isset($_SESSION['error'])) {
        $output .= showAlert($_SESSION['error'], 'error');
        unset($_SESSION['error']);
    }
    
    if (isset($_SESSION['warning'])) {
        $output .= showAlert($_SESSION['warning'], 'warning');
        unset($_SESSION['warning']);
    }
    
    if (isset($_SESSION['info'])) {
        $output .= showAlert($_SESSION['info'], 'info');
        unset($_SESSION['info']);
    }
    
    return $output;
}

?>
