<?php
/**
 * ملف إنشاء مدير جديد
 * Create New Admin Script
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إنشاء مدير جديد</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-5'>";

echo "<h1 class='text-center mb-4'>👤 إنشاء مدير جديد</h1>";
echo "<div class='card'>";
echo "<div class='card-body'>";

try {
    $db = Database::getInstance();
    
    // فحص وجود مديرين
    $adminCount = $db->query("SELECT COUNT(*) as count FROM admins")->fetch();
    echo "<p>عدد المديرين الحاليين: " . $adminCount['count'] . "</p>";
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $username = $_POST['username'] ?? '';
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        $full_name = $_POST['full_name'] ?? '';
        
        if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
            echo "<div class='alert alert-danger'>جميع الحقول مطلوبة</div>";
        } else {
            // فحص وجود المستخدم
            $existing = $db->query("SELECT id FROM admins WHERE username = ? OR email = ?", [$username, $email])->fetch();
            
            if ($existing) {
                echo "<div class='alert alert-danger'>اسم المستخدم أو البريد الإلكتروني موجود بالفعل</div>";
            } else {
                // إنشاء المدير الجديد
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                $db->query("INSERT INTO admins (username, email, password, full_name, role, status) VALUES (?, ?, ?, ?, 'super_admin', 'active')", 
                    [$username, $email, $hashedPassword, $full_name]);
                
                echo "<div class='alert alert-success'>";
                echo "<h4>✅ تم إنشاء المدير بنجاح!</h4>";
                echo "<p><strong>اسم المستخدم:</strong> " . htmlspecialchars($username) . "</p>";
                echo "<p><strong>البريد الإلكتروني:</strong> " . htmlspecialchars($email) . "</p>";
                echo "<p><strong>كلمة المرور:</strong> " . htmlspecialchars($password) . "</p>";
                echo "</div>";
                
                echo "<div class='text-center'>";
                echo "<a href='login.php' class='btn btn-primary'>تسجيل الدخول</a>";
                echo "</div>";
            }
        }
    } else {
        // عرض النموذج
        echo "<form method='POST'>";
        echo "<div class='mb-3'>";
        echo "<label for='username' class='form-label'>اسم المستخدم</label>";
        echo "<input type='text' class='form-control' id='username' name='username' required>";
        echo "</div>";
        
        echo "<div class='mb-3'>";
        echo "<label for='email' class='form-label'>البريد الإلكتروني</label>";
        echo "<input type='email' class='form-control' id='email' name='email' required>";
        echo "</div>";
        
        echo "<div class='mb-3'>";
        echo "<label for='password' class='form-label'>كلمة المرور</label>";
        echo "<input type='password' class='form-control' id='password' name='password' required>";
        echo "</div>";
        
        echo "<div class='mb-3'>";
        echo "<label for='full_name' class='form-label'>الاسم الكامل</label>";
        echo "<input type='text' class='form-control' id='full_name' name='full_name' required>";
        echo "</div>";
        
        echo "<div class='d-grid'>";
        echo "<button type='submit' class='btn btn-primary'>إنشاء المدير</button>";
        echo "</div>";
        echo "</form>";
        
        // إضافة مدير افتراضي سريع
        if ($adminCount['count'] == 0) {
            echo "<hr>";
            echo "<div class='alert alert-info'>";
            echo "<h5>إنشاء مدير افتراضي سريع</h5>";
            echo "<p>يمكنك إنشاء مدير افتراضي بالبيانات التالية:</p>";
            echo "<ul>";
            echo "<li><strong>اسم المستخدم:</strong> admin</li>";
            echo "<li><strong>البريد الإلكتروني:</strong> <EMAIL></li>";
            echo "<li><strong>كلمة المرور:</strong> password</li>";
            echo "<li><strong>الاسم الكامل:</strong> المدير العام</li>";
            echo "</ul>";
            echo "<form method='POST'>";
            echo "<input type='hidden' name='username' value='admin'>";
            echo "<input type='hidden' name='email' value='<EMAIL>'>";
            echo "<input type='hidden' name='password' value='password'>";
            echo "<input type='hidden' name='full_name' value='المدير العام'>";
            echo "<button type='submit' class='btn btn-warning'>إنشاء المدير الافتراضي</button>";
            echo "</form>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ خطأ</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>"; // card-body
echo "</div>"; // card
echo "</div>"; // container
echo "</body>";
echo "</html>";
?>
