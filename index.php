<?php
/**
 * الصفحة الرئيسية لموقع الإعلانات
 * Homepage for Classified Ads Website
 */

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

// فحص وجود الملفات قبل تحميلها
if (!file_exists('includes/config.php')) {
    die('ملف config.php غير موجود. يرجى تشغيل <a href="setup.php">setup.php</a> أولاً.');
}

require_once 'includes/config.php';

if (!file_exists('includes/functions.php')) {
    die('ملف functions.php غير موجود.');
}

require_once 'includes/functions.php';

if (!file_exists('includes/models.php')) {
    die('ملف models.php غير موجود.');
}

require_once 'includes/models.php';

// الحصول على الإعلانات المميزة
try {
    $featuredAds = getAds(['featured' => true], 1, 8);
} catch (Exception $e) {
    $featuredAds = ['data' => [], 'total_items' => 0];
}

// الحصول على أحدث الإعلانات
try {
    $latestAds = getAds([], 1, 12);
} catch (Exception $e) {
    $latestAds = ['data' => [], 'total_items' => 0];
}

// الحصول على الأقسام الرئيسية
try {
    $categories = getCategoryTree();
} catch (Exception $e) {
    $categories = [];
}

// إحصائيات الموقع
try {
    $db = Database::getInstance();
    $stats = [
        'total_ads' => $db->count('ads', ['status = ?'], ['active']),
        'total_users' => $db->count('users', ['status = ?'], ['active']),
        'total_categories' => $db->count('categories', ['status = ?'], ['active'])
    ];
} catch (Exception $e) {
    $stats = [
        'total_ads' => 0,
        'total_users' => 0,
        'total_categories' => 0
    ];
}

$pageTitle = getSetting('site_name', 'موقع الإعلانات');
$pageDescription = getSetting('site_description', 'موقع إعلانات مبوبة شامل');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <meta name="keywords" content="<?php echo getSetting('site_keywords', 'إعلانات, مبوبة, بيع, شراء'); ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo asset('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="<?php echo url(); ?>">
                    <i class="fas fa-bullhorn me-2"></i>
                    <?php echo getSetting('site_name', 'موقع الإعلانات'); ?>
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="<?php echo url(); ?>">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo url('ads.php'); ?>">جميع الإعلانات</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                الأقسام
                            </a>
                            <ul class="dropdown-menu">
                                <?php foreach ($categories as $category): ?>
                                    <li>
                                        <a class="dropdown-item" href="<?php echo url('category.php?id=' . $category['id']); ?>">
                                            <i class="<?php echo $category['icon']; ?> me-2"></i>
                                            <?php echo $category['name_ar']; ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </li>
                    </ul>
                    
                    <ul class="navbar-nav">
                        <?php if (isLoggedIn()): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-1"></i>
                                    <?php echo $_SESSION['user_name']; ?>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="<?php echo url('profile.php'); ?>">حسابي</a></li>
                                    <li><a class="dropdown-item" href="<?php echo url('my-ads.php'); ?>">إعلاناتي</a></li>
                                    <li><a class="dropdown-item" href="<?php echo url('favorites.php'); ?>">المفضلة</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo url('logout.php'); ?>">تسجيل الخروج</a></li>
                                </ul>
                            </li>
                        <?php else: ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo url('login.php'); ?>">تسجيل الدخول</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo url('register.php'); ?>">إنشاء حساب</a>
                            </li>
                        <?php endif; ?>
                        
                        <li class="nav-item">
                            <a class="btn btn-warning ms-2" href="<?php echo url('add-ad.php'); ?>">
                                <i class="fas fa-plus me-1"></i>
                                إضافة إعلان
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero bg-light py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold text-primary mb-4">
                        اكتشف أفضل الإعلانات المبوبة
                    </h1>
                    <p class="lead mb-4">
                        ابحث عن كل ما تحتاجه من عقارات، وظائف، سيارات، وأكثر في مكان واحد
                    </p>
                    
                    <!-- Search Form -->
                    <form action="<?php echo url('search.php'); ?>" method="GET" class="search-form">
                        <div class="input-group input-group-lg">
                            <input type="text" class="form-control" name="q" placeholder="ابحث عن إعلان...">
                            <select class="form-select" name="category" style="max-width: 200px;">
                                <option value="">جميع الأقسام</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>">
                                        <?php echo $category['name_ar']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="col-lg-6">
                    <div class="hero-stats">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-card">
                                    <h3 class="text-primary"><?php echo formatNumber($stats['total_ads']); ?></h3>
                                    <p class="mb-0">إعلان نشط</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-card">
                                    <h3 class="text-success"><?php echo formatNumber($stats['total_users']); ?></h3>
                                    <p class="mb-0">مستخدم مسجل</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-card">
                                    <h3 class="text-info"><?php echo formatNumber($stats['total_categories']); ?></h3>
                                    <p class="mb-0">قسم متاح</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="categories py-5">
        <div class="container">
            <h2 class="text-center mb-5">تصفح حسب القسم</h2>
            <div class="row">
                <?php foreach ($categories as $category): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <a href="<?php echo url('category.php?id=' . $category['id']); ?>" class="category-card text-decoration-none">
                            <div class="card h-100 text-center">
                                <div class="card-body">
                                    <i class="<?php echo $category['icon']; ?> fa-3x text-primary mb-3"></i>
                                    <h5 class="card-title"><?php echo $category['name_ar']; ?></h5>
                                    <p class="card-text text-muted">
                                        <?php 
                                        $adCount = $db->count('ads', ['category_id = ? AND status = ?'], [$category['id'], 'active']);
                                        echo formatNumber($adCount) . ' إعلان';
                                        ?>
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Featured Ads Section -->
    <?php if (!empty($featuredAds['data'])): ?>
    <section class="featured-ads py-5 bg-light">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>الإعلانات المميزة</h2>
                <a href="<?php echo url('ads.php?featured=1'); ?>" class="btn btn-outline-primary">
                    عرض الكل <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
            
            <div class="row">
                <?php foreach ($featuredAds['data'] as $ad): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <?php include 'includes/ad-card.php'; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Latest Ads Section -->
    <section class="latest-ads py-5">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>أحدث الإعلانات</h2>
                <a href="<?php echo url('ads.php'); ?>" class="btn btn-outline-primary">
                    عرض الكل <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
            
            <div class="row">
                <?php foreach ($latestAds['data'] as $ad): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <?php include 'includes/ad-card.php'; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer bg-dark text-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><?php echo getSetting('site_name', 'موقع الإعلانات'); ?></h5>
                    <p><?php echo getSetting('site_description', 'موقع إعلانات مبوبة شامل'); ?></p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 mb-4">
                    <h6>روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo url(); ?>" class="text-light">الرئيسية</a></li>
                        <li><a href="<?php echo url('ads.php'); ?>" class="text-light">الإعلانات</a></li>
                        <li><a href="<?php echo url('add-ad.php'); ?>" class="text-light">إضافة إعلان</a></li>
                        <li><a href="<?php echo url('contact.php'); ?>" class="text-light">اتصل بنا</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 mb-4">
                    <h6>الأقسام</h6>
                    <ul class="list-unstyled">
                        <?php foreach (array_slice($categories, 0, 4) as $category): ?>
                            <li>
                                <a href="<?php echo url('category.php?id=' . $category['id']); ?>" class="text-light">
                                    <?php echo $category['name_ar']; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <h6>اتصل بنا</h6>
                    <p><i class="fas fa-envelope me-2"></i> <?php echo getSetting('admin_email', '<EMAIL>'); ?></p>
                    <p><i class="fas fa-phone me-2"></i> +966 50 123 4567</p>
                    <p><i class="fas fa-map-marker-alt me-2"></i> الرياض، المملكة العربية السعودية</p>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo getSetting('site_name', 'موقع الإعلانات'); ?>. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="<?php echo url('privacy.php'); ?>" class="text-light me-3">سياسة الخصوصية</a>
                    <a href="<?php echo url('terms.php'); ?>" class="text-light">شروط الاستخدام</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo asset('js/main.js'); ?>"></script>
</body>
</html>
