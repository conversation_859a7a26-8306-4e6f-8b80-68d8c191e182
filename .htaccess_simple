# ملف .htaccess مبسط وآمن
# Simple and Safe .htaccess file

# تفعيل إعادة الكتابة
RewriteEngine On

# منع عرض محتويات المجلدات
Options -Indexes

# حماية الملفات الحساسة
<Files "*.log">
    Deny from all
</Files>

<Files "*.sql">
    Deny from all
</Files>

<Files "config.php">
    Deny from all
</Files>

# URLs صديقة لمحركات البحث (اختياري)
# RewriteRule ^ad/([0-9]+)/(.*)$ ad-details.php?id=$1 [L,QSA]
# RewriteRule ^category/([0-9]+)/(.*)$ category.php?id=$1 [L,QSA]

# إعدادات PHP الأساسية
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
</IfModule>

# صفحات الخطأ المخصصة (اختياري)
# ErrorDocument 404 /errors/404.php
# ErrorDocument 500 /errors/500.php
