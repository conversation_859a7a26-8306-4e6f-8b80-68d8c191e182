<?php
/**
 * ملف اختبار الوصول للوحة الإدارة
 * Admin Access Test File
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار الوصول للوحة الإدارة</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-5'>";

echo "<h1 class='text-center mb-4'>🔍 اختبار الوصول للوحة الإدارة</h1>";
echo "<div class='card'>";
echo "<div class='card-body'>";

// اختبار تحميل الملفات المطلوبة
echo "<h3>1. اختبار تحميل الملفات:</h3>";

$files = [
    '../includes/config.php',
    '../includes/database.php', 
    '../includes/functions.php',
    '../includes/models.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p>✅ $file موجود</p>";
        try {
            require_once $file;
            echo "<p>✅ تم تحميل $file بنجاح</p>";
        } catch (Exception $e) {
            echo "<p>❌ خطأ في تحميل $file: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ $file غير موجود</p>";
    }
}

// اختبار الاتصال بقاعدة البيانات
echo "<h3>2. اختبار الاتصال بقاعدة البيانات:</h3>";
try {
    $db = Database::getInstance();
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // فحص جدول المديرين
    $admins = $db->query("SELECT COUNT(*) as count FROM admins")->fetch();
    echo "<p>📊 عدد المديرين في قاعدة البيانات: " . $admins['count'] . "</p>";
    
    if ($admins['count'] == 0) {
        echo "<div class='alert alert-warning'>";
        echo "<h5>⚠️ لا يوجد مديرين في قاعدة البيانات</h5>";
        echo "<p>يجب إنشاء حساب مدير أولاً</p>";
        echo "</div>";
    } else {
        // عرض المديرين الموجودين
        $adminList = $db->query("SELECT username, email, role, status FROM admins")->fetchAll();
        echo "<h4>المديرين الموجودين:</h4>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>اسم المستخدم</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr></thead>";
        echo "<tbody>";
        foreach ($adminList as $admin) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['role']) . "</td>";
            echo "<td>" . htmlspecialchars($admin['status']) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار الجلسة
echo "<h3>3. اختبار الجلسة:</h3>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p>✅ الجلسة نشطة</p>";
    echo "<p>معرف الجلسة: " . session_id() . "</p>";
} else {
    echo "<p>❌ الجلسة غير نشطة</p>";
}

// اختبار الثوابت
echo "<h3>4. اختبار الثوابت:</h3>";
$constants = ['DB_HOST', 'DB_NAME', 'DB_USER', 'SITE_URL', 'DEBUG_MODE'];
foreach ($constants as $const) {
    if (defined($const)) {
        echo "<p>✅ $const = " . constant($const) . "</p>";
    } else {
        echo "<p>❌ $const غير معرف</p>";
    }
}

echo "<div class='mt-4'>";
echo "<a href='login.php' class='btn btn-primary'>صفحة تسجيل الدخول</a> ";
echo "<a href='../setup.php' class='btn btn-warning'>إعداد قاعدة البيانات</a>";
echo "</div>";

echo "</div>"; // card-body
echo "</div>"; // card
echo "</div>"; // container
echo "</body>";
echo "</html>";
?>
