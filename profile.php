<?php
/**
 * صفحة الملف الشخصي
 * User Profile Page
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/models.php';

// التحقق من تسجيل الدخول
requireLogin();

$error = '';
$success = '';
$user = getCurrentUser();

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $updateData = [
            'full_name' => trim($_POST['full_name'] ?? ''),
            'phone' => trim($_POST['phone'] ?? ''),
            'user_type' => $_POST['user_type'] ?? 'individual'
        ];
        
        // التحقق من البيانات
        if (empty($updateData['full_name'])) {
            $error = 'الاسم الكامل مطلوب';
        } else {
            $result = updateUserProfile($_SESSION['user_id'], $updateData);
            if ($result['success']) {
                $success = 'تم تحديث بياناتك بنجاح';
                $user = getCurrentUser(); // إعادة تحميل البيانات
            } else {
                $error = $result['message'];
            }
        }
    } elseif ($action === 'change_password') {
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $error = 'جميع حقول كلمة المرور مطلوبة';
        } elseif ($newPassword !== $confirmPassword) {
            $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
        } elseif (strlen($newPassword) < 8) {
            $error = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        } else {
            $result = changeUserPassword($_SESSION['user_id'], $currentPassword, $newPassword);
            if ($result['success']) {
                $success = 'تم تغيير كلمة المرور بنجاح';
            } else {
                $error = $result['message'];
            }
        }
    }
}

// الحصول على إحصائيات المستخدم
$db = Database::getInstance();
$userStats = [
    'total_ads' => $db->count('ads', ['user_id = ?'], [$_SESSION['user_id']]),
    'active_ads' => $db->count('ads', ['user_id = ? AND status = ?'], [$_SESSION['user_id'], 'active']),
    'pending_ads' => $db->count('ads', ['user_id = ? AND status = ?'], [$_SESSION['user_id'], 'pending']),
    'expired_ads' => $db->count('ads', ['user_id = ? AND status = ?'], [$_SESSION['user_id'], 'expired'])
];

$pageTitle = 'الملف الشخصي';
$breadcrumb = [
    ['title' => 'الملف الشخصي']
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . getSetting('site_name'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo asset('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container py-5">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="avatar-container mb-3">
                            <?php if ($user['avatar']): ?>
                                <img src="<?php echo asset('uploads/users/' . $user['avatar']); ?>" 
                                     alt="الصورة الشخصية" class="rounded-circle" width="100" height="100">
                            <?php else: ?>
                                <div class="avatar-placeholder rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto" 
                                     style="width: 100px; height: 100px; font-size: 2rem;">
                                    <?php echo mb_substr($user['full_name'], 0, 1); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <h5><?php echo htmlspecialchars($user['full_name']); ?></h5>
                        <p class="text-muted"><?php echo htmlspecialchars($user['email']); ?></p>
                        <span class="badge bg-<?php echo $user['user_type'] === 'business' ? 'warning' : 'info'; ?>">
                            <?php echo $user['user_type'] === 'business' ? 'شركة' : 'فرد'; ?>
                        </span>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="card mt-3">
                    <div class="list-group list-group-flush">
                        <a href="profile.php" class="list-group-item list-group-item-action active">
                            <i class="fas fa-user me-2"></i>
                            الملف الشخصي
                        </a>
                        <a href="my-ads.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-bullhorn me-2"></i>
                            إعلاناتي
                            <span class="badge bg-primary rounded-pill float-end"><?php echo $userStats['total_ads']; ?></span>
                        </a>
                        <a href="favorites.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-heart me-2"></i>
                            المفضلة
                        </a>
                        <a href="messages.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-envelope me-2"></i>
                            الرسائل
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-lg-9">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-bullhorn fa-2x text-primary mb-2"></i>
                                <h4><?php echo $userStats['total_ads']; ?></h4>
                                <small class="text-muted">إجمالي الإعلانات</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h4><?php echo $userStats['active_ads']; ?></h4>
                                <small class="text-muted">نشطة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h4><?php echo $userStats['pending_ads']; ?></h4>
                                <small class="text-muted">في الانتظار</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                                <h4><?php echo $userStats['expired_ads']; ?></h4>
                                <small class="text-muted">منتهية الصلاحية</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Profile Form -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            تحديث البيانات الشخصية
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" 
                                           value="<?php echo htmlspecialchars($user['email']); ?>" disabled>
                                    <div class="form-text">لا يمكن تغيير البريد الإلكتروني</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع الحساب</label>
                                    <div class="mt-2">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="user_type" id="individual" 
                                                   value="individual" <?php echo $user['user_type'] === 'individual' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="individual">فرد</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="user_type" id="business" 
                                                   value="business" <?php echo $user['user_type'] === 'business' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="business">شركة</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Change Password -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-lock me-2"></i>
                            تغيير كلمة المرور
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>
                                تغيير كلمة المرور
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include 'includes/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo asset('js/main.js'); ?>"></script>
</body>
</html>
