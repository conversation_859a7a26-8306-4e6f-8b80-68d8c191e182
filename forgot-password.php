<?php
/**
 * صفحة استعادة كلمة المرور
 * Forgot Password Page
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/models.php';

// إعادة توجيه المستخدم المسجل للصفحة الرئيسية
if (isLoggedIn()) {
    redirect('index.php');
}

$error = '';
$success = '';
$step = $_GET['step'] ?? 'request';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 'request') {
        // طلب إعادة تعيين كلمة المرور
        $email = trim($_POST['email'] ?? '');
        
        if (empty($email)) {
            $error = 'يرجى إدخال البريد الإلكتروني';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'البريد الإلكتروني غير صحيح';
        } else {
            $result = requestPasswordReset($email);
            if ($result['success']) {
                $success = 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني';
            } else {
                $error = $result['message'];
            }
        }
    } elseif ($step === 'reset') {
        // إعادة تعيين كلمة المرور
        $token = $_POST['token'] ?? '';
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        if (empty($token) || empty($password) || empty($confirmPassword)) {
            $error = 'جميع الحقول مطلوبة';
        } elseif ($password !== $confirmPassword) {
            $error = 'كلمات المرور غير متطابقة';
        } elseif (strlen($password) < 8) {
            $error = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        } else {
            $result = resetPassword($token, $password);
            if ($result['success']) {
                $success = 'تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول';
                $step = 'success';
            } else {
                $error = $result['message'];
            }
        }
    }
}

$pageTitle = 'استعادة كلمة المرور';
$breadcrumb = [
    ['title' => 'استعادة كلمة المرور']
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . getSetting('site_name'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo asset('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-key fa-3x text-primary mb-3"></i>
                            <h2>استعادة كلمة المرور</h2>
                            <?php if ($step === 'request'): ?>
                                <p class="text-muted">أدخل بريدك الإلكتروني لإرسال رابط إعادة التعيين</p>
                            <?php elseif ($step === 'reset'): ?>
                                <p class="text-muted">أدخل كلمة المرور الجديدة</p>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($step === 'request' && !$success): ?>
                            <!-- Request Reset Form -->
                            <form method="POST" action="">
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-envelope"></i>
                                        </span>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        إرسال رابط الاستعادة
                                    </button>
                                </div>
                            </form>
                            
                        <?php elseif ($step === 'reset' && !$success): ?>
                            <!-- Reset Password Form -->
                            <form method="POST" action="">
                                <input type="hidden" name="token" value="<?php echo htmlspecialchars($_GET['token'] ?? ''); ?>">
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور الجديدة</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">يجب أن تحتوي على 8 أحرف على الأقل</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-2"></i>
                                        تغيير كلمة المرور
                                    </button>
                                </div>
                            </form>
                            
                        <?php elseif ($step === 'success' || $success): ?>
                            <!-- Success Message -->
                            <div class="text-center">
                                <div class="mb-4">
                                    <i class="fas fa-check-circle fa-4x text-success"></i>
                                </div>
                                <h4 class="text-success mb-3">تم بنجاح!</h4>
                                <p class="mb-4">تم تغيير كلمة المرور بنجاح</p>
                                <a href="login.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-0">
                                تذكرت كلمة المرور؟ 
                                <a href="login.php" class="text-decoration-none fw-bold">
                                    تسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include 'includes/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo asset('js/main.js'); ?>"></script>
    
    <script>
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }
    
    // Password confirmation validation
    const confirmPassword = document.getElementById('confirm_password');
    if (confirmPassword) {
        confirmPassword.addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPasswordValue = this.value;
            
            if (password !== confirmPasswordValue) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    }
    </script>
</body>
</html>
